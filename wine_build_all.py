#!/usr/bin/env python3
"""
一键设置Wine环境并构建Windows .exe文件
"""
import os
import subprocess
import sys
import platform

def run_command(cmd, description):
    """运行命令"""
    print(f"\n🔄 {description}...")
    print(f"命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查系统
    if platform.system().lower() != "darwin":
        print("⚠️  此脚本专为macOS设计")
        print("Linux用户请手动安装wine并运行相应脚本")
        return False
    
    # 检查必要文件
    required_files = ['web_gui.py', 'apis.py', 'jwt_generator.py', 'cache.py', 'file_selector.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 前置条件检查通过")
    return True

def install_wine():
    """安装Wine"""
    print("\n🍷 安装Wine...")
    
    # 检查Homebrew
    try:
        subprocess.run(["brew", "--version"], check=True, capture_output=True)
        print("✅ Homebrew已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("📦 安装Homebrew...")
        cmd = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
        if not run_command(cmd, "安装Homebrew"):
            return False
    
    # 安装Wine
    if not run_command("brew install --cask wine-stable", "安装Wine"):
        print("⚠️  Wine安装失败，可能已经安装或需要手动处理")
    
    # 安装winetricks
    if not run_command("brew install winetricks", "安装winetricks"):
        print("⚠️  winetricks安装失败，但可能不影响构建")
    
    return True

def setup_wine_python():
    """设置Wine Python环境"""
    print("\n🐍 设置Wine Python环境...")
    
    # 运行Python安装脚本
    if not run_command("python3 setup_wine_python.py", "设置Wine Python环境"):
        print("❌ Wine Python环境设置失败")
        return False
    
    return True

def build_windows_exe():
    """构建Windows .exe"""
    print("\n🔨 构建Windows .exe文件...")
    
    if not run_command("python3 build_wine.py", "构建Windows .exe"):
        print("❌ Windows .exe构建失败")
        return False
    
    return True

def main():
    """主函数"""
    print("🍷 Wine一键Windows构建工具")
    print("=" * 50)
    print("此工具将:")
    print("1. 安装Wine（如果需要）")
    print("2. 在Wine中安装Python和依赖")
    print("3. 使用Wine构建Windows .exe文件")
    print()
    
    # 确认继续
    response = input("是否继续？这可能需要20-30分钟 (y/N): ")
    if response.lower() != 'y':
        print("👋 操作取消")
        return
    
    # 检查前置条件
    if not check_prerequisites():
        return False
    
    # 安装Wine
    if not install_wine():
        print("❌ Wine安装失败")
        return False
    
    # 设置Wine Python环境
    if not setup_wine_python():
        print("❌ Wine Python环境设置失败")
        return False
    
    # 构建Windows .exe
    if not build_windows_exe():
        print("❌ Windows .exe构建失败")
        return False
    
    print("\n🎉 一键构建完成！")
    print("📁 Windows .exe文件位于: dist/GooglePlayAPITool.exe")
    print("📤 可以将整个dist文件夹发送给Windows用户")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
