# Wine Windows构建指南

## 概述

使用Wine在macOS上构建Windows .exe文件，无需Windows虚拟机或物理机器。

## 快速开始

### 方法一：一键构建（推荐）
```bash
python3 wine_build_all.py
```

### 方法二：分步构建
```bash
# 1. 安装Wine
bash setup_wine_build.sh

# 2. 设置Python环境
python3 setup_wine_python.py

# 3. 构建Windows .exe
python3 build_wine.py
```

## 详细步骤

### 1. 系统要求
- macOS 10.14或更高版本
- 至少4GB可用磁盘空间
- 稳定的网络连接（需要下载Python安装程序）

### 2. 安装过程

#### 第一步：安装Wine
```bash
# 自动安装Homebrew和Wine
bash setup_wine_build.sh
```

这将：
- 安装Homebrew（如果没有）
- 安装Wine Stable版本
- 安装winetricks辅助工具
- 初始化Wine环境

#### 第二步：设置Python环境
```bash
python3 setup_wine_python.py
```

这将：
- 下载Python 3.9.13 Windows版本
- 在Wine中安装Python
- 安装PyInstaller和所有依赖
- 验证环境设置

#### 第三步：构建Windows .exe
```bash
python3 build_wine.py
```

这将：
- 检查Wine环境
- 使用Wine中的PyInstaller构建.exe文件
- 创建完整的Windows发布包

### 3. 构建结果

成功后会生成：
```
dist/
├── GooglePlayAPITool.exe    # Windows可执行文件
├── start.bat               # Windows启动脚本
└── README_Windows.md       # Windows使用说明
```

## 优势和限制

### 优势
✅ 无需Windows系统或虚拟机
✅ 在macOS上直接构建Windows .exe
✅ 自动化程度高
✅ 构建的.exe文件与原生Windows构建兼容

### 限制
⚠️ 首次设置时间较长（20-30分钟）
⚠️ 需要下载较大的文件（Python安装程序等）
⚠️ Wine环境可能不如原生Windows稳定
⚠️ 某些复杂依赖可能有兼容性问题

## 故障排除

### Wine安装失败
```bash
# 手动安装Wine
brew install --cask wine-stable

# 如果还是失败，尝试清理Homebrew缓存
brew cleanup
brew update
```

### Python安装失败
```bash
# 手动下载Python安装程序
curl -O https://www.python.org/ftp/python/3.9.13/python-3.9.13-amd64.exe

# 手动在Wine中安装
wine python-3.9.13-amd64.exe /quiet InstallAllUsers=1 PrependPath=1
```

### PyInstaller构建失败
```bash
# 检查Wine Python环境
wine python --version
wine python -m pip list

# 重新安装PyInstaller
wine python -m pip install --force-reinstall PyInstaller
```

### 生成的.exe文件无法运行
1. 确保目标Windows系统有Visual C++ Redistributable
2. 检查杀毒软件是否阻止
3. 尝试在不同版本的Windows上测试

## 性能优化

### 减少构建时间
```bash
# 使用本地缓存的Python安装程序
export WINE_PYTHON_CACHE=/path/to/cache

# 并行构建（如果支持）
export MAKEFLAGS="-j$(nproc)"
```

### 减少文件大小
在`build_wine.py`中添加更多排除模块：
```python
"--exclude-module=tkinter",
"--exclude-module=matplotlib", 
"--exclude-module=numpy",
"--exclude-module=pandas",
"--exclude-module=scipy",
```

## 验证构建结果

### 在macOS上验证
```bash
# 检查文件类型
file dist/GooglePlayAPITool.exe
# 应该显示: PE32+ executable (console) x86-64, for MS Windows

# 检查依赖
wine dist/GooglePlayAPITool.exe --help
```

### 在Windows上验证
1. 将dist文件夹复制到Windows机器
2. 双击start.bat或GooglePlayAPITool.exe
3. 验证程序正常启动和功能

## 自动化构建

### CI/CD集成
可以将Wine构建集成到GitHub Actions中：

```yaml
name: Build Windows EXE
on: [push]
jobs:
  build:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup Wine
      run: |
        brew install --cask wine-stable
        python3 setup_wine_python.py
    - name: Build EXE
      run: python3 build_wine.py
    - name: Upload artifact
      uses: actions/upload-artifact@v2
      with:
        name: windows-exe
        path: dist/
```

## 总结

Wine方案提供了在macOS上构建Windows .exe文件的可行方法，虽然设置过程较复杂，但一旦配置完成，就可以方便地进行跨平台构建。

对于偶尔需要构建Windows版本的开发者来说，这是一个很好的替代方案，避免了维护Windows虚拟机的开销。
