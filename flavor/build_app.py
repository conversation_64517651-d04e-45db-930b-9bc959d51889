#!/usr/bin/env python3
"""
打包脚本 - 将Google Play API工具打包成独立可执行文件
"""
import os
import subprocess
import sys
import shutil

def build_executable():
    print("🚀 开始打包Google Play API工具...")
    
    # 检查必要文件
    required_files = ['web_gui.py', 'apis.py', 'jwt_generator.py', 'cache.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 创建打包目录
    if os.path.exists('../dist'):
        shutil.rmtree('../dist')
    if os.path.exists('../build'):
        shutil.rmtree('../build')
    
    # PyInstaller命令 - 修复Windows打包问题
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 打包成单个文件
        '--console',  # 保留控制台窗口，便于调试和查看错误
        '--name=GooglePlayAPITool',  # 可执行文件名
        '--add-data=apis.py:.',  # 包含依赖文件
        '--add-data=jwt_generator.py:.',
        '--add-data=cache.py:.',
        '--add-data=file_selector.py:.',  # 添加文件选择器
        '--hidden-import=jwt',
        '--hidden-import=requests',
        '--hidden-import=cryptography',
        '--hidden-import=json',
        '--hidden-import=threading',
        '--hidden-import=webbrowser',
        '--hidden-import=socketserver',
        '--hidden-import=http.server',
        '--hidden-import=urllib.parse',
        '--hidden-import=base64',
        '--hidden-import=hashlib',
        '--hidden-import=hmac',
        '--hidden-import=time',
        '--hidden-import=datetime',
        '--hidden-import=platform',
        '--hidden-import=subprocess',
        '--hidden-import=socket',
        '--hidden-import=select',
        '--collect-all=jwt',  # 收集jwt模块的所有依赖
        '--collect-all=cryptography',  # 收集cryptography模块的所有依赖
        '--collect-all=requests',  # 收集requests模块的所有依赖
        'web_gui.py'
    ]
    
    print("📦 执行打包命令...")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        
        # 检查生成的文件
        import platform
        current_system = platform.system().lower()

        if current_system == "windows":
            exe_name = "GooglePlayAPITool.exe"
        else:
            exe_name = "GooglePlayAPITool"

        exe_path = os.path.join('../dist', exe_name)
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 可执行文件位置: {exe_path}")
            print(f"📏 文件大小: {size:.1f} MB")
            print(f"🖥️  目标平台: {platform.system()}")

            # 创建使用说明
            create_readme()

            print("\n🎉 打包完成!")
            print("📋 分发说明:")

            if current_system == "windows":
                print("1. 将整个 dist 文件夹发送给Windows用户")
                print("2. 用户双击 start.bat 或 GooglePlayAPITool.exe 运行")
            else:
                print("⚠️  注意: 当前在非Windows系统上打包")
                print("1. 生成的文件只能在相同系统上运行")
                print("2. 要生成Windows .exe文件，需要在Windows系统上运行此脚本")
                print("3. 当前文件适用于: " + platform.system())

            print("4. 用户需要准备自己的 Google API JSON 文件")
            print("5. 在网页界面中选择文件并输入相关信息")

        else:
            print("❌ 未找到生成的可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = """# Google Play Developer API Tool

## 使用说明

### 1. 准备工作
- 确保你有 Google Play Developer API 的 JSON 凭证文件
- 该文件可以是任意名称（如：googleapi.json, service-account.json 等）

### 2. 运行程序

**Windows用户：**
- 双击 `GooglePlayAPITool.exe` 可执行文件
- 会弹出控制台窗口显示启动信息
- 程序会自动启动本地服务器并尝试打开浏览器
- 如果浏览器没有自动打开，请手动访问显示的网址（通常是 http://localhost:8080）

**macOS/Linux用户：**
- 在终端中运行 `./GooglePlayAPITool`
- 或者双击可执行文件（如果系统支持）

### 3. 使用界面
1. **选择API文件**：
   - 点击 "Google API JSON File" 下的文件选择按钮
   - 选择你的Google API凭证JSON文件
   - 系统会自动验证文件格式和必要字段

2. **输入信息**：
   - Package Name: 应用包名（如：com.example.app）
   - Order ID: Google Play 订单ID（如：GPA.1234-5678-9012-34567）

3. **执行操作**：
   - 点击 "Generate JWT Token" 生成访问令牌（可选）
   - 点击 "1. Get Order Info" 获取订单信息
   - 点击 "2. Consume Product" 消费产品
   - 点击 "3. Product Details" 获取产品详情
   - 或者点击 "Run All Steps" 一次性执行所有步骤

4. **查看结果**：
   - 所有操作结果会显示在 "Output" 区域
   - 输入的信息会自动保存，下次打开时会恢复

### 4. 停止程序
- 关闭浏览器标签页
- 在控制台窗口中按 Ctrl+C 停止服务器
- 或者直接关闭控制台窗口

### 5. 故障排除

**程序无法启动：**
- Windows: 确保在"安全性与隐私"设置中允许运行未知发布者的程序
- 检查是否有杀毒软件阻止程序运行
- 尝试以管理员身份运行

**端口被占用：**
- 程序会自动寻找可用端口（8080-8099, 9000-9019）
- 如果所有端口都被占用，请关闭占用端口的其他程序

**浏览器无法打开：**
- 手动访问控制台显示的网址
- 确保防火墙没有阻止本地连接

**API调用失败：**
- 确保Google API JSON文件格式正确且有效
- 检查API权限是否正确配置
- 确认网络连接正常

### 6. 技术支持
如有问题，请检查：
1. 控制台窗口的错误信息
2. 浏览器界面Output区域的日志
3. Google API凭证文件是否有效
4. 网络连接是否正常

### 7. 安全提醒
- Google API凭证文件包含敏感信息，请妥善保管
- 不要将凭证文件分享给他人
- 建议定期轮换API密钥
"""
    
    with open('dist/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("📝 已创建使用说明文件: dist/README.md")

if __name__ == "__main__":
    success = build_executable()
    if not success:
        sys.exit(1)
