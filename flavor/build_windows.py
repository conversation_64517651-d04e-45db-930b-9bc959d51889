#!/usr/bin/env python3
"""
Windows专用打包脚本 - 修复Windows打包和运行问题
"""
import os
import subprocess
import sys
import shutil
import platform

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyInstaller版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ PyInstaller未正确安装")
            return False
    except Exception as e:
        print(f"❌ 检查PyInstaller时出错: {e}")
        return False

def install_dependencies():
    """安装必要的依赖"""
    dependencies = ['PyInstaller', 'PyJWT', 'requests', 'cryptography']
    
    print("📦 检查并安装依赖...")
    for dep in dependencies:
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {dep} 已安装/更新")
            else:
                print(f"❌ 安装 {dep} 失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 安装 {dep} 时出错: {e}")
            return False
    
    return True

def build_windows_executable():
    """构建Windows可执行文件"""
    print("🚀 开始构建Windows可执行文件...")
    
    # 检查必要文件
    required_files = ['web_gui.py', 'apis.py', 'jwt_generator.py', 'cache.py', 'file_selector.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 清理旧的构建文件
    for dir_name in ['dist', 'build']:
        if os.path.exists(dir_name):
            print(f"🧹 清理旧的构建目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 删除旧的spec文件
    spec_file = '../GooglePlayAPITool.spec'
    if os.path.exists(spec_file):
        os.remove(spec_file)
    
    # PyInstaller命令 - Windows优化版本
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',  # 打包成单个文件
        '--console',  # 保留控制台窗口，便于调试
        '--name=GooglePlayAPITool',  # 可执行文件名
        '--icon=NONE',  # 不使用图标，避免图标相关问题
        
        # 添加数据文件
        '--add-data=apis.py:.',
        '--add-data=jwt_generator.py:.',
        '--add-data=cache.py:.',
        '--add-data=file_selector.py:.',
        
        # 隐式导入 - 确保所有依赖都被包含
        '--hidden-import=jwt',
        '--hidden-import=jwt.algorithms',
        '--hidden-import=jwt.exceptions',
        '--hidden-import=requests',
        '--hidden-import=requests.adapters',
        '--hidden-import=requests.auth',
        '--hidden-import=requests.exceptions',
        '--hidden-import=cryptography',
        '--hidden-import=cryptography.hazmat',
        '--hidden-import=cryptography.hazmat.primitives',
        '--hidden-import=cryptography.hazmat.primitives.asymmetric',
        '--hidden-import=cryptography.hazmat.primitives.serialization',
        '--hidden-import=json',
        '--hidden-import=threading',
        '--hidden-import=webbrowser',
        '--hidden-import=socketserver',
        '--hidden-import=http.server',
        '--hidden-import=urllib.parse',
        '--hidden-import=base64',
        '--hidden-import=hashlib',
        '--hidden-import=hmac',
        '--hidden-import=time',
        '--hidden-import=datetime',
        '--hidden-import=platform',
        '--hidden-import=subprocess',
        '--hidden-import=socket',
        '--hidden-import=select',
        '--hidden-import=logging',
        '--hidden-import=traceback',
        
        # 收集所有相关模块
        '--collect-all=jwt',
        '--collect-all=cryptography',
        '--collect-all=requests',
        
        # 排除不需要的模块以减小文件大小
        '--exclude-module=tkinter',
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
        
        # 主文件
        'web_gui.py'
    ]
    
    print("📦 执行打包命令...")
    print("⏳ 这可能需要几分钟时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        
        # 检查生成的文件
        exe_path = os.path.join('../dist', 'GooglePlayAPITool.exe')
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 可执行文件位置: {exe_path}")
            print(f"📏 文件大小: {size:.1f} MB")
            
            # 复制启动脚本
            if os.path.exists('start_windows.bat'):
                shutil.copy2('start_windows.bat', 'dist/start.bat')
                print("✅ 已复制启动脚本")
            
            # 创建使用说明
            create_windows_readme()
            
            print("\n🎉 Windows版本打包完成!")
            print("📋 分发说明:")
            print("1. 将整个 dist 文件夹发送给Windows用户")
            print("2. 用户可以双击 start.bat 启动程序")
            print("3. 或者直接双击 GooglePlayAPITool.exe")
            print("4. 用户需要准备自己的 Google API JSON 文件")
            
            return True
            
        else:
            print("❌ 未找到生成的可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

def create_windows_readme():
    """创建Windows专用说明文件"""
    readme_content = """# Google Play Developer API Tool - Windows版本

## 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start.bat` 文件
2. 按照屏幕提示操作

### 方法二：直接运行
1. 双击 `GooglePlayAPITool.exe` 文件
2. 程序会打开控制台窗口并启动Web服务器
3. 浏览器会自动打开，如果没有请手动访问显示的网址

## 使用说明

1. **准备Google API凭证文件**
   - 从Google Cloud Console下载JSON格式的服务账号凭证
   - 文件名可以是任意名称（如：googleapi.json）

2. **使用程序**
   - 在网页界面中点击"选择文件"按钮
   - 选择你的Google API JSON凭证文件
   - 输入应用包名和订单ID
   - 点击相应按钮执行操作

3. **查看结果**
   - 所有操作结果会显示在网页的Output区域
   - 控制台窗口也会显示详细的运行日志

## 故障排除

### 程序无法启动
- **杀毒软件阻止**：将程序添加到杀毒软件白名单
- **权限问题**：右键选择"以管理员身份运行"
- **系统兼容性**：确保Windows版本支持（Windows 7及以上）

### 浏览器无法打开
- 手动访问控制台显示的网址（通常是 http://localhost:8080）
- 检查防火墙是否阻止本地连接

### API调用失败
- 确保Google API JSON文件格式正确
- 检查网络连接
- 确认API权限配置正确

## 安全提醒
- Google API凭证文件包含敏感信息，请妥善保管
- 不要将凭证文件分享给他人

## 技术支持
如有问题，请查看控制台窗口的错误信息，或联系技术支持。
"""
    
    with open('dist/README_Windows.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("📝 已创建Windows使用说明文件")

def main():
    """主函数"""
    print("🪟 Google Play API Tool - Windows构建脚本")
    print("=" * 50)
    
    # 检查是否在Windows上运行
    if platform.system().lower() != 'windows':
        print("⚠️  警告: 此脚本专为Windows设计，当前系统可能不兼容")
        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            return
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        input("按回车键退出...")
        return
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("❌ PyInstaller检查失败")
        input("按回车键退出...")
        return
    
    # 构建可执行文件
    success = build_windows_executable()
    
    if success:
        print("\n🎉 构建完成!")
        print("📁 可执行文件位于 dist/ 目录")
    else:
        print("\n❌ 构建失败")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
