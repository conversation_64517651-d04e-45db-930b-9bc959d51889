#!/usr/bin/env python3
"""
创建发布包 - 将应用打包成完整的分发包
"""
import os
import shutil
import zipfile
from datetime import datetime

def create_release_package():
    print("📦 创建发布包...")

    # 检测平台并创建对应的发布目录
    import platform
    system = platform.system().lower()

    if system == "windows":
        release_dir = "GooglePlayAPITool_Release_Windows"
        exe_name = "GooglePlayAPITool.exe"
    else:
        release_dir = "GooglePlayAPITool_Release"
        exe_name = "GooglePlayAPITool"

    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)

    # 复制可执行文件
    exe_path = f'dist/{exe_name}'
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, release_dir)
        print(f"✅ 已复制可执行文件: {exe_name}")
    else:
        print(f"❌ 未找到可执行文件: {exe_path}，请先运行 build_app.py")
        return False
    
    # 创建示例配置文件
    create_sample_config(release_dir)
    
    # 创建启动脚本
    create_launcher_script(release_dir)
    
    # 创建详细说明文档
    create_detailed_readme(release_dir)
    
    # 创建ZIP压缩包
    create_zip_package(release_dir)
    
    print(f"\n🎉 发布包创建完成!")
    print(f"📁 发布目录: {release_dir}/")
    print(f"📦 压缩包: {release_dir}.zip")
    print("\n📋 分发说明:")
    print("1. 发送 GooglePlayAPITool_Release.zip 给用户")
    print("2. 用户解压后按照 README.md 说明使用")
    
    return True

def create_sample_config(release_dir):
    """创建示例配置文件"""
    sample_config = *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    
    import json
    with open(os.path.join(release_dir, 'googleapi_sample.json'), 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print("✅ 已创建示例配置文件")

def create_launcher_script(release_dir):
    """创建启动脚本"""
    import platform
    system = platform.system().lower()

    if system == "windows":
        # Windows 启动脚本 - 增强版
        launcher_bat = """@echo off
chcp 65001 >nul
title Google Play Developer API Tool

echo.
echo ========================================
echo   Google Play Developer API Tool
echo ========================================
echo.
echo 🚀 正在启动程序...
echo.

REM 检查可执行文件是否存在
if not exist "GooglePlayAPITool.exe" (
    echo ❌ 错误: 未找到 GooglePlayAPITool.exe
    echo.
    echo 请确保以下文件在同一目录:
    echo - GooglePlayAPITool.exe
    echo - start.bat (本文件)
    echo.
    pause
    exit /b 1
)

echo ✅ 找到程序文件
echo.

REM 显示使用提示
echo 📋 使用提示:
echo - 程序启动后会自动打开浏览器
echo - 如果浏览器没有自动打开，请手动访问显示的网址
echo - 在网页界面中选择Google API JSON凭证文件
echo - 输入包名和订单ID后执行相关操作
echo.

echo 🌐 启动Web服务器...
echo.

REM 启动程序
GooglePlayAPITool.exe --auto

REM 程序结束后的处理
echo.
echo ========================================
echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ 程序正常退出
) else (
    echo ❌ 程序异常退出 (错误代码: %ERRORLEVEL%)
    echo.
    echo 💡 故障排除建议:
    echo 1. 检查是否有杀毒软件阻止程序运行
    echo 2. 尝试以管理员身份运行此脚本
    echo 3. 确保网络连接正常
    echo 4. 检查防火墙设置
)

echo.
echo 👋 感谢使用 Google Play API Tool
echo.
pause
"""

        with open(os.path.join(release_dir, 'start.bat'), 'w', encoding='utf-8') as f:
            f.write(launcher_bat)

        print("✅ 已创建Windows启动脚本")

    else:
        # macOS/Linux 启动脚本
        launcher_sh = """#!/bin/bash
echo "🚀 启动 Google Play API Tool..."
echo "📋 使用提示:"
echo "- 程序启动后会自动打开浏览器"
echo "- 如果浏览器没有自动打开，请手动访问显示的网址"
echo "- 在网页界面中选择Google API JSON凭证文件"
echo ""

# 检查可执行文件
if [ ! -f "./GooglePlayAPITool" ]; then
    echo "❌ 错误: 未找到 GooglePlayAPITool 可执行文件"
    echo "请确保文件在当前目录并具有执行权限"
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ 找到程序文件"
echo "🌐 启动Web服务器..."
echo ""

# 启动应用
./GooglePlayAPITool --auto

echo ""
echo "👋 应用已退出"
read -p "按回车键关闭..."
"""

        launcher_path = os.path.join(release_dir, 'start.sh')
        with open(launcher_path, 'w') as f:
            f.write(launcher_sh)

        # 设置执行权限
        os.chmod(launcher_path, 0o755)

        print("✅ 已创建macOS/Linux启动脚本")

def create_detailed_readme(release_dir):
    """创建详细说明文档"""
    readme_content = f"""# Google Play Developer API Tool

**版本**: 1.0  
**构建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**平台**: macOS / Linux

## 📋 功能介绍

这是一个用于管理 Google Play Developer API 的工具，主要功能包括：

- 🔍 **获取订单信息** - 查询 Google Play 内购订单详情
- 🛒 **消费产品** - 标记内购产品为已消费状态  
- 📊 **产品详情** - 获取内购产品的详细信息
- 🚀 **一键执行** - 自动完成所有操作流程

## 🚀 快速开始

### 1. 准备 Google API 凭证

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建或选择项目
3. 启用 Google Play Developer API
4. 创建服务账号并下载 JSON 凭证文件
5. 将凭证文件重命名为 `googleapi.json` 并放在本工具目录下

### 2. 运行程序

**方法一：使用启动脚本（推荐）**
```bash
# macOS/Linux
./start.sh

# Windows  
start.bat
```

**方法二：直接运行**
```bash
./GooglePlayAPITool
```

### 3. 使用界面

1. 程序启动后会自动打开浏览器访问 http://localhost:8080
2. 在界面中输入：
   - **Package Name**: 应用包名（如：com.example.app）
   - **Order ID**: Google Play 订单ID（如：GPA.1234-5678-9012-34567）
3. 选择操作：
   - 单步执行：按顺序点击 1→2→3
   - 一键执行：点击 "Run All Steps"

## 📁 文件说明

```
GooglePlayAPITool_Release/
├── GooglePlayAPITool          # 主程序（可执行文件）
├── start.sh                   # macOS/Linux 启动脚本
├── start.bat                  # Windows 启动脚本  
├── googleapi_sample.json      # 配置文件示例
├── README.md                  # 本说明文件
└── googleapi.json            # 您的 API 凭证文件（需要您提供）
```

## 🔧 故障排除

### 常见问题

**Q: 浏览器没有自动打开？**  
A: 手动访问 http://localhost:8080

**Q: 提示端口被占用？**  
A: 关闭占用 8080 端口的其他程序，或重启计算机

**Q: API 调用失败？**  
A: 检查 googleapi.json 文件是否正确，确认 API 权限已正确配置

**Q: 程序无法启动？**  
A: 确认系统权限，在 macOS 上可能需要在"安全性与隐私"中允许运行

### 日志查看

程序运行时的详细日志会显示在：
- 启动脚本的控制台窗口
- 浏览器界面的 Output 区域

## 🔒 安全说明

- `googleapi.json` 文件包含敏感信息，请妥善保管
- 不要将凭证文件分享给他人
- 建议定期轮换 API 密钥

## 📞 技术支持

如遇到问题，请检查：
1. Google API 凭证是否有效
2. 网络连接是否正常
3. 控制台错误信息

## 📄 许可证

本工具仅供学习和合法商业用途使用。
"""
    
    with open(os.path.join(release_dir, 'README.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建详细说明文档")

def create_zip_package(release_dir):
    """创建ZIP压缩包"""
    zip_name = f"{release_dir}.zip"
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, release_dir)
                zipf.write(file_path, arc_name)
    
    # 获取压缩包大小
    size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    print(f"✅ 已创建压缩包: {zip_name} ({size:.1f} MB)")

if __name__ == "__main__":
    create_release_package()
