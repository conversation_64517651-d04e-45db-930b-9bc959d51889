#!/usr/bin/env python3
"""
检查当前打包状态和系统信息
"""
import os
import platform
import sys

def check_system_info():
    """检查系统信息"""
    print("🖥️  系统信息")
    print("=" * 30)
    print(f"操作系统: {platform.system()}")
    print(f"系统版本: {platform.release()}")
    print(f"架构: {platform.machine()}")
    print(f"平台: {platform.platform()}")
    print(f"Python版本: {sys.version}")
    print()

def check_source_files():
    """检查源文件"""
    print("📁 源文件检查")
    print("=" * 30)
    
    required_files = [
        'web_gui.py',
        'apis.py', 
        'jwt_generator.py',
        'cache.py',
        'file_selector.py'
    ]
    
    optional_files = [
        'build_app.py',
        'build_windows.py',
        'build_and_test.py',
        'test_build.py',
        'start_windows.bat'
    ]
    
    print("必需文件:")
    all_required_exist = True
    for file_name in required_files:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"  ✅ {file_name} ({size} bytes)")
        else:
            print(f"  ❌ {file_name}")
            all_required_exist = False
    
    print("\n可选文件:")
    for file_name in optional_files:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"  ✅ {file_name} ({size} bytes)")
        else:
            print(f"  ⚪ {file_name}")
    
    return all_required_exist

def check_build_output():
    """检查构建输出"""
    print("\n🔨 构建输出检查")
    print("=" * 30)
    
    if not os.path.exists('dist'):
        print("❌ dist 目录不存在")
        print("   请先运行打包脚本")
        return False
    
    print("dist 目录内容:")
    dist_files = os.listdir('dist')
    
    if not dist_files:
        print("  ❌ dist 目录为空")
        return False
    
    current_system = platform.system().lower()
    expected_exe = "GooglePlayAPITool.exe" if current_system == "windows" else "GooglePlayAPITool"
    
    exe_found = False
    for file_name in dist_files:
        file_path = os.path.join('dist', file_name)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            if file_name == expected_exe:
                print(f"  ✅ {file_name} ({size:.1f} MB) - 主程序")
                exe_found = True
            else:
                print(f"  📄 {file_name} ({size:.1f} MB)")
        else:
            print(f"  📁 {file_name}/ (目录)")
    
    return exe_found

def check_dependencies():
    """检查依赖"""
    print("\n📦 依赖检查")
    print("=" * 30)
    
    dependencies = [
        ('PyInstaller', 'PyInstaller'),
        ('PyJWT', 'jwt'),
        ('requests', 'requests'),
        ('cryptography', 'cryptography')
    ]
    
    all_deps_ok = True
    for dep_name, import_name in dependencies:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"  ✅ {dep_name} ({version})")
        except ImportError:
            print(f"  ❌ {dep_name} - 未安装")
            all_deps_ok = False
    
    return all_deps_ok

def provide_recommendations():
    """提供建议"""
    print("\n💡 建议和下一步")
    print("=" * 30)
    
    current_system = platform.system().lower()
    
    if current_system == "windows":
        print("✅ 你在Windows系统上，可以直接打包Windows版本")
        print("   运行: python build_windows.py")
    else:
        print(f"⚠️  你在 {platform.system()} 系统上")
        print("   当前只能打包适用于当前系统的版本")
        print("   要生成Windows .exe文件，需要:")
        print("   1. 在Windows机器上运行打包脚本")
        print("   2. 使用Windows虚拟机")
        print("   3. 使用Windows云服务器")
    
    print("\n📋 推荐的操作步骤:")
    
    if os.path.exists('dist/GooglePlayAPITool') or os.path.exists('dist/GooglePlayAPITool.exe'):
        print("1. ✅ 已有构建版本，可以测试功能")
        if current_system != "windows":
            print("2. 📋 查看 WINDOWS_PACKAGING_INSTRUCTIONS.md 了解Windows打包方法")
        print("3. 🧪 运行 python test_build.py 测试当前版本")
    else:
        print("1. 🔨 运行打包脚本:")
        if current_system == "windows":
            print("   python build_windows.py")
        else:
            print("   python build_app.py")
        print("2. 🧪 运行 python test_build.py 测试结果")

def main():
    """主函数"""
    print("🔍 Google Play API Tool - 构建状态检查")
    print("=" * 50)
    
    # 检查系统信息
    check_system_info()
    
    # 检查源文件
    source_ok = check_source_files()
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    # 检查构建输出
    build_ok = check_build_output()
    
    # 提供建议
    provide_recommendations()
    
    # 总结
    print("\n📊 检查总结")
    print("=" * 30)
    print(f"源文件: {'✅ 完整' if source_ok else '❌ 缺失'}")
    print(f"依赖: {'✅ 完整' if deps_ok else '❌ 缺失'}")
    print(f"构建输出: {'✅ 存在' if build_ok else '❌ 不存在'}")
    
    if source_ok and deps_ok:
        if build_ok:
            print("\n🎉 状态良好，可以进行测试或分发")
        else:
            print("\n🔨 准备就绪，可以开始打包")
    else:
        print("\n⚠️  需要解决依赖或文件问题")

if __name__ == "__main__":
    main()
    
    # 在Windows上等待用户确认
    if platform.system().lower() == "windows":
        input("\n按回车键退出...")
