#!/bin/bash
# Wine + PyInstaller Windows构建环境设置脚本

echo "🍷 设置Wine + PyInstaller Windows构建环境"
echo "=" * 50

# 检查是否在macOS上
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "⚠️  此脚本专为macOS设计"
    echo "Linux用户请使用包管理器安装wine"
    exit 1
fi

# 1. 安装Homebrew（如果没有）
if ! command -v brew &> /dev/null; then
    echo "📦 安装Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "✅ Homebrew已安装"
fi

# 2. 安装Wine
echo "🍷 安装Wine..."
brew install --cask wine-stable

# 3. 安装winetricks（Wine辅助工具）
echo "🔧 安装winetricks..."
brew install winetricks

# 4. 初始化Wine环境
echo "🔄 初始化Wine环境..."
winecfg

echo "✅ Wine安装完成！"
echo ""
echo "📋 下一步："
echo "1. 运行 python3 setup_wine_python.py 安装Windows Python"
echo "2. 运行 python3 build_wine.py 进行Windows构建"
