#!/usr/bin/env python3
"""
在Wine环境中安装Windows版本的Python和依赖
"""
import os
import subprocess
import sys
import urllib.request
import tempfile

def run_wine_command(cmd, description):
    """运行Wine命令"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def download_file(url, filename):
    """下载文件"""
    print(f"📥 下载 {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"✅ 下载完成: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def setup_wine_python():
    """在Wine中设置Python环境"""
    print("🐍 在Wine中安装Python...")
    
    # 1. 下载Python Windows安装程序
    python_version = "3.9.13"  # 使用稳定版本
    python_url = f"https://www.python.org/ftp/python/{python_version}/python-{python_version}-amd64.exe"
    python_installer = f"python-{python_version}-amd64.exe"
    
    with tempfile.TemporaryDirectory() as temp_dir:
        os.chdir(temp_dir)
        
        if not download_file(python_url, python_installer):
            return False
        
        # 2. 在Wine中安装Python
        install_cmd = f"wine {python_installer} /quiet InstallAllUsers=1 PrependPath=1"
        if not run_wine_command(install_cmd, "安装Python"):
            return False
        
        # 3. 验证Python安装
        if not run_wine_command("wine python --version", "验证Python安装"):
            return False
        
        # 4. 升级pip
        if not run_wine_command("wine python -m pip install --upgrade pip", "升级pip"):
            return False
        
        # 5. 安装必要的依赖
        dependencies = [
            "PyInstaller>=5.0",
            "PyJWT>=2.0", 
            "requests>=2.25",
            "cryptography>=3.0"
        ]
        
        for dep in dependencies:
            if not run_wine_command(f"wine python -m pip install {dep}", f"安装{dep}"):
                print(f"⚠️  {dep}安装失败，但继续...")
        
        print("✅ Wine Python环境设置完成！")
        return True

def test_wine_environment():
    """测试Wine环境"""
    print("🧪 测试Wine环境...")
    
    tests = [
        ("wine --version", "Wine版本"),
        ("wine python --version", "Python版本"),
        ("wine python -m pip --version", "pip版本"),
        ("wine python -c \"import PyInstaller; print('PyInstaller:', PyInstaller.__version__)\"", "PyInstaller"),
        ("wine python -c \"import jwt; print('PyJWT:', jwt.__version__)\"", "PyJWT"),
        ("wine python -c \"import requests; print('requests:', requests.__version__)\"", "requests"),
        ("wine python -c \"import cryptography; print('cryptography:', cryptography.__version__)\"", "cryptography")
    ]
    
    success_count = 0
    for cmd, desc in tests:
        if run_wine_command(cmd, f"测试{desc}"):
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{len(tests)} 通过")
    return success_count == len(tests)

def main():
    """主函数"""
    print("🍷 Wine Python环境设置")
    print("=" * 40)
    
    # 检查Wine是否安装
    try:
        subprocess.run(["wine", "--version"], check=True, capture_output=True)
        print("✅ Wine已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Wine未安装")
        print("请先运行: bash setup_wine_build.sh")
        return False
    
    # 设置Python环境
    if not setup_wine_python():
        print("❌ Python环境设置失败")
        return False
    
    # 测试环境
    if test_wine_environment():
        print("\n🎉 Wine Python环境设置成功！")
        print("现在可以运行: python3 build_wine.py")
        return True
    else:
        print("\n⚠️  环境测试部分失败，但可能仍然可用")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
