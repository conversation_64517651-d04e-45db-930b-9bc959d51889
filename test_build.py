#!/usr/bin/env python3
"""
测试打包后的程序是否能正常工作
"""
import os
import subprocess
import sys
import time
import platform
import requests
import threading

def test_executable():
    """测试可执行文件"""
    system = platform.system().lower()
    
    if system == "windows":
        exe_path = "dist/GooglePlayAPITool.exe"
    else:
        exe_path = "dist/GooglePlayAPITool"
    
    if not os.path.exists(exe_path):
        print(f"❌ 可执行文件不存在: {exe_path}")
        print("请先运行构建脚本")
        return False
    
    print(f"✅ 找到可执行文件: {exe_path}")
    
    # 启动程序
    print("🚀 启动程序进行测试...")
    
    try:
        # 使用自动模式启动，避免交互
        process = subprocess.Popen(
            [exe_path, "--auto"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 等待程序启动
        print("⏳ 等待程序启动...")
        time.sleep(5)
        
        # 检查程序是否还在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print("❌ 程序启动后立即退出")
            print(f"标准输出: {stdout}")
            print(f"错误输出: {stderr}")
            return False
        
        # 测试Web服务器是否响应
        print("🌐 测试Web服务器...")
        
        # 尝试多个端口
        ports_to_test = [8080, 8081, 8082, 9000]
        server_found = False
        
        for port in ports_to_test:
            try:
                response = requests.get(f"http://localhost:{port}", timeout=3)
                if response.status_code == 200:
                    print(f"✅ Web服务器在端口 {port} 正常响应")
                    print(f"响应长度: {len(response.text)} 字符")
                    server_found = True
                    break
            except requests.exceptions.RequestException:
                continue
        
        if not server_found:
            print("❌ Web服务器无响应")
            # 尝试获取程序输出
            try:
                stdout, stderr = process.communicate(timeout=2)
                print(f"程序输出: {stdout}")
                print(f"错误输出: {stderr}")
            except subprocess.TimeoutExpired:
                print("程序仍在运行，但Web服务器无响应")
            
            process.terminate()
            return False
        
        # 测试API端点
        print("🔧 测试API端点...")
        
        # 测试文件信息端点
        try:
            api_response = requests.post(
                f"http://localhost:{port}/api/file_info",
                json={},
                timeout=3
            )
            if api_response.status_code == 200:
                print("✅ API端点正常响应")
            else:
                print(f"⚠️  API端点响应异常: {api_response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"⚠️  API端点测试失败: {e}")
        
        # 停止程序
        print("🛑 停止测试程序...")
        process.terminate()
        
        # 等待程序结束
        try:
            process.wait(timeout=5)
            print("✅ 程序正常结束")
        except subprocess.TimeoutExpired:
            print("⚠️  程序未能及时结束，强制终止")
            process.kill()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_dependencies():
    """测试依赖是否正确打包"""
    print("📦 测试依赖打包...")
    
    # 这些是程序需要的关键模块
    required_modules = [
        'jwt',
        'requests',
        'cryptography',
        'json',
        'threading',
        'webbrowser',
        'socketserver',
        'http.server'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"⚠️  缺少模块: {', '.join(missing_modules)}")
        print("这些模块可能需要在打包时特别处理")
        return False
    
    print("✅ 所有依赖模块都可用")
    return True

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    system = platform.system().lower()
    
    if system == "windows":
        expected_files = [
            "dist/GooglePlayAPITool.exe",
            "dist/start.bat",
            "dist/README_Windows.md"
        ]
    else:
        expected_files = [
            "dist/GooglePlayAPITool",
            "dist/README.md"
        ]
    
    missing_files = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"⚠️  缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 Google Play API Tool - 构建测试")
    print("=" * 50)
    
    # 检查文件结构
    if not check_file_structure():
        print("❌ 文件结构检查失败")
        return False
    
    # 测试依赖
    if not test_dependencies():
        print("❌ 依赖测试失败")
        return False
    
    # 测试可执行文件
    if not test_executable():
        print("❌ 可执行文件测试失败")
        return False
    
    print("\n🎉 所有测试通过!")
    print("✅ 程序构建成功，可以正常运行")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if platform.system().lower() == "windows":
        input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
