#!/usr/bin/env python3
"""
完整的构建和测试流程
"""
import os
import sys
import subprocess
import platform
import shutil

def run_script(script_name, description):
    """运行脚本并返回结果"""
    print(f"\n{'='*20} {description} {'='*20}")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {description} 成功")
        if result.stdout:
            print("输出:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        if e.stdout:
            print("标准输出:")
            print(e.stdout)
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ 未找到脚本: {script_name}")
        return False

def clean_build_directories():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['dist', 'build', '__pycache__']
    files_to_clean = ['GooglePlayAPITool.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✅ 已删除文件: {file_name}")

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    
    # 检查必要文件
    required_files = [
        'web_gui.py',
        'apis.py', 
        'jwt_generator.py',
        'cache.py',
        'file_selector.py'
    ]
    
    missing_files = []
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name}")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    return True

def install_build_dependencies():
    """安装构建依赖"""
    print("📦 安装构建依赖...")
    
    dependencies = [
        'PyInstaller>=5.0',
        'PyJWT>=2.0',
        'requests>=2.25',
        'cryptography>=3.0'
    ]
    
    for dep in dependencies:
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep],
                                  capture_output=True, text=True, check=True)
            print(f"✅ {dep}")
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装 {dep} 失败: {e.stderr}")
            return False
    
    return True

def main():
    """主函数"""
    print("🏗️  Google Play API Tool - 完整构建流程")
    print("=" * 60)
    
    system = platform.system().lower()
    print(f"操作系统: {platform.system()} {platform.release()}")
    
    # 1. 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败")
        return False
    
    # 2. 安装依赖
    if not install_build_dependencies():
        print("❌ 依赖安装失败")
        return False
    
    # 3. 清理旧文件
    clean_build_directories()
    
    # 4. 选择构建脚本
    if system == "windows":
        build_script = "build_windows.py"
        if not os.path.exists(build_script):
            build_script = "build_app.py"
    else:
        build_script = "build_app.py"
    
    # 5. 执行构建
    if not run_script(build_script, f"构建{system}版本"):
        print("❌ 构建失败")
        return False
    
    # 6. 运行测试
    if os.path.exists("test_build.py"):
        if not run_script("test_build.py", "测试构建结果"):
            print("⚠️  测试失败，但构建可能仍然可用")
    else:
        print("⚠️  未找到测试脚本，跳过测试")
    
    # 7. 创建发布包
    if os.path.exists("create_release.py"):
        if run_script("create_release.py", "创建发布包"):
            print("✅ 发布包创建成功")
        else:
            print("⚠️  发布包创建失败，但可执行文件可用")
    
    print("\n🎉 构建流程完成!")
    
    # 显示结果
    if system == "windows":
        exe_path = "dist/GooglePlayAPITool.exe"
    else:
        exe_path = "dist/GooglePlayAPITool"
    
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"📁 可执行文件: {exe_path} ({size:.1f} MB)")
        
        print("\n📋 使用说明:")
        if system == "windows":
            print("1. 双击 dist/start.bat 启动程序（推荐）")
            print("2. 或者双击 dist/GooglePlayAPITool.exe")
        else:
            print("1. 运行 ./dist/GooglePlayAPITool")
            print("2. 或者使用发布包中的启动脚本")
        
        print("3. 在网页界面中选择Google API JSON文件")
        print("4. 输入包名和订单ID执行操作")
        
        return True
    else:
        print("❌ 未找到可执行文件")
        return False

if __name__ == "__main__":
    success = main()
    
    if platform.system().lower() == "windows":
        input("\n按回车键退出...")
    
    sys.exit(0 if success else 1)
