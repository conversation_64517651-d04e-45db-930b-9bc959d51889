#!/usr/bin/env python3
"""
使用Wine在macOS上构建Windows .exe文件
"""
import os
import subprocess
import sys
import shutil
import platform

def run_wine_command(cmd, description, capture_output=True):
    """运行Wine命令"""
    print(f"🔄 {description}...")
    try:
        if capture_output:
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            print(f"✅ {description}成功")
            return result
        else:
            result = subprocess.run(cmd, shell=True, check=True)
            print(f"✅ {description}成功")
            return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if capture_output and e.stdout:
            print(f"输出: {e.stdout}")
        if capture_output and e.stderr:
            print(f"错误: {e.stderr}")
        return None

def check_wine_environment():
    """检查Wine环境"""
    print("🔍 检查Wine环境...")
    
    checks = [
        ("wine --version", "Wine"),
        ("wine python --version", "Python"),
        ("wine python -m PyInstaller --version", "PyInstaller")
    ]
    
    for cmd, name in checks:
        result = run_wine_command(cmd, f"检查{name}")
        if not result:
            return False
    
    return True

def prepare_build_environment():
    """准备构建环境"""
    print("📁 准备构建环境...")
    
    # 检查必要文件
    required_files = ['web_gui.py', 'apis.py', 'jwt_generator.py', 'cache.py', 'file_selector.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 清理旧的构建文件
    for dir_name in ['dist', 'build']:
        if os.path.exists(dir_name):
            print(f"🧹 清理旧的构建目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 删除旧的spec文件
    spec_file = 'GooglePlayAPITool.spec'
    if os.path.exists(spec_file):
        os.remove(spec_file)
        print("🧹 清理旧的spec文件")
    
    return True

def build_windows_exe():
    """使用Wine构建Windows .exe文件"""
    print("🔨 使用Wine构建Windows .exe文件...")
    
    # PyInstaller命令
    cmd_parts = [
        "wine python -m PyInstaller",
        "--onefile",
        "--console", 
        "--name=GooglePlayAPITool",
        "--add-data=apis.py:.",
        "--add-data=jwt_generator.py:.",
        "--add-data=cache.py:.",
        "--add-data=file_selector.py:.",
        "--hidden-import=jwt",
        "--hidden-import=jwt.algorithms",
        "--hidden-import=jwt.exceptions", 
        "--hidden-import=requests",
        "--hidden-import=requests.adapters",
        "--hidden-import=requests.auth",
        "--hidden-import=requests.exceptions",
        "--hidden-import=cryptography",
        "--hidden-import=cryptography.hazmat",
        "--hidden-import=cryptography.hazmat.primitives",
        "--hidden-import=cryptography.hazmat.primitives.asymmetric",
        "--hidden-import=cryptography.hazmat.primitives.serialization",
        "--hidden-import=json",
        "--hidden-import=threading",
        "--hidden-import=webbrowser",
        "--hidden-import=socketserver",
        "--hidden-import=http.server",
        "--hidden-import=urllib.parse",
        "--hidden-import=base64",
        "--hidden-import=hashlib",
        "--hidden-import=hmac",
        "--hidden-import=time",
        "--hidden-import=datetime",
        "--hidden-import=platform",
        "--hidden-import=subprocess",
        "--hidden-import=socket",
        "--hidden-import=select",
        "--hidden-import=logging",
        "--hidden-import=traceback",
        "--collect-all=jwt",
        "--collect-all=cryptography", 
        "--collect-all=requests",
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "web_gui.py"
    ]
    
    cmd = " ".join(cmd_parts)
    
    print("📦 执行Wine PyInstaller命令...")
    print("⏳ 这可能需要几分钟时间，请耐心等待...")
    
    # 运行构建命令（不捕获输出，让用户看到进度）
    result = run_wine_command(cmd, "Wine PyInstaller构建", capture_output=False)
    
    if not result:
        return False
    
    # 检查生成的文件
    exe_path = os.path.join('dist', 'GooglePlayAPITool.exe')
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"📁 Windows可执行文件: {exe_path}")
        print(f"📏 文件大小: {size:.1f} MB")
        return True
    else:
        print("❌ 未找到生成的.exe文件")
        return False

def create_windows_package():
    """创建Windows发布包"""
    print("📦 创建Windows发布包...")
    
    # 复制启动脚本
    if os.path.exists('flavor/start_windows.bat'):
        shutil.copy2('flavor/start_windows.bat', 'dist/start.bat')
        print("✅ 已复制启动脚本")
    
    # 创建README
    readme_content = """# Google Play Developer API Tool - Windows版本

## 使用方法

1. 双击 start.bat 启动程序（推荐）
2. 或者直接双击 GooglePlayAPITool.exe

## 注意事项

- 此文件使用Wine在macOS上构建
- 在Windows上应该可以正常运行
- 如有问题，请尝试安装Visual C++ Redistributable

## 故障排除

如果程序无法启动：
1. 确保Windows版本支持（Windows 7及以上）
2. 安装最新的Visual C++ Redistributable
3. 检查杀毒软件是否阻止程序运行
4. 尝试以管理员身份运行
"""
    
    with open('dist/README_Windows.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建Windows使用说明")

def main():
    """主函数"""
    print("🍷 Wine Windows构建工具")
    print("=" * 40)
    
    # 检查当前系统
    if platform.system().lower() == "windows":
        print("⚠️  你在Windows系统上，可以直接使用build_windows.py")
        print("Wine构建主要用于macOS/Linux系统")
        response = input("是否继续？(y/N): ")
        if response.lower() != 'y':
            return
    
    # 检查Wine环境
    if not check_wine_environment():
        print("❌ Wine环境检查失败")
        print("请先运行:")
        print("1. bash setup_wine_build.sh")
        print("2. python3 setup_wine_python.py")
        return False
    
    # 准备构建环境
    if not prepare_build_environment():
        print("❌ 构建环境准备失败")
        return False
    
    # 构建Windows .exe
    if not build_windows_exe():
        print("❌ Windows .exe构建失败")
        return False
    
    # 创建发布包
    create_windows_package()
    
    print("\n🎉 Wine Windows构建完成！")
    print("📋 结果:")
    print("- Windows .exe文件: dist/GooglePlayAPITool.exe")
    print("- 启动脚本: dist/start.bat") 
    print("- 使用说明: dist/README_Windows.md")
    print("\n📤 可以将整个dist文件夹发送给Windows用户")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
