@echo off
chcp 65001 >nul
title Google Play API Tool

echo.
echo ========================================
echo   Google Play Developer API Tool
echo ========================================
echo.
echo 🚀 正在启动程序...
echo.

REM 检查可执行文件是否存在
if not exist "GooglePlayAPITool.exe" (
    echo ❌ 错误: 未找到 GooglePlayAPITool.exe
    echo.
    echo 请确保以下文件在同一目录:
    echo - GooglePlayAPITool.exe
    echo - start_windows.bat (本文件)
    echo.
    pause
    exit /b 1
)

echo ✅ 找到程序文件
echo.

REM 显示使用提示
echo 📋 使用提示:
echo - 程序启动后会自动打开浏览器
echo - 如果浏览器没有自动打开，请手动访问显示的网址
echo - 需要准备Google API JSON凭证文件
echo - 在网页界面中选择凭证文件并输入相关信息
echo.

echo 🌐 启动Web服务器...
echo.

REM 启动程序
GooglePlayAPITool.exe --auto

REM 程序结束后的处理
echo.
echo ========================================
echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ 程序正常退出
) else (
    echo ❌ 程序异常退出 (错误代码: %ERRORLEVEL%)
    echo.
    echo 💡 故障排除建议:
    echo 1. 检查是否有杀毒软件阻止程序运行
    echo 2. 尝试以管理员身份运行此脚本
    echo 3. 确保网络连接正常
    echo 4. 检查防火墙设置
)

echo.
echo 👋 感谢使用 Google Play API Tool
echo.
pause
